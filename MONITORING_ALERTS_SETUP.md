# Monitoring & Alerts Setup Guide

## 📊 Available Monitoring Tools

### 1. **Firebase Console (Built-in)**
- Function execution metrics
- Error rates and logs
- Performance monitoring
- Usage statistics

### 2. **Google Cloud Monitoring**
- Advanced metrics and dashboards
- Custom alerts and notifications
- Log-based metrics
- SLA monitoring

### 3. **Third-party Tools**
- Sentry for error tracking
- DataDog for comprehensive monitoring
- New Relic for performance monitoring

## 🔥 Firebase Console Monitoring

### Access Firebase Monitoring:
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your `towasl` project
3. Navigate to **Functions** → **Logs** and **Usage**

### Key Metrics to Monitor:
- **Invocations**: Number of function calls
- **Execution time**: Function performance
- **Memory usage**: Resource consumption
- **Error rate**: Failed requests percentage
- **Billing**: Cost tracking

## 🚨 Setting Up Alerts

### 1. Firebase Alerts (Basic)

#### Enable Email Notifications:
1. Go to **Project Settings** → **General**
2. Add your email under **Project members**
3. Set notification preferences

#### Budget Alerts:
1. Go to **Project Settings** → **Usage and billing**
2. Set up **Budget alerts**
3. Configure thresholds (e.g., $10, $50, $100)

### 2. Google Cloud Monitoring (Advanced)

#### Setup Steps:
```bash
# Enable Cloud Monitoring API
gcloud services enable monitoring.googleapis.com

# Install monitoring client (optional)
npm install @google-cloud/monitoring
```

#### Create Custom Alerts:

1. **Go to Google Cloud Console**
2. **Navigate to Monitoring → Alerting**
3. **Create Alert Policies**

#### Example Alert Configurations:

**High Error Rate Alert:**
```yaml
Display Name: "High Error Rate - Towasl Functions"
Condition: 
  - Resource: Cloud Function
  - Metric: executions/count (with status=error)
  - Threshold: > 10 errors in 5 minutes
Notification: Email, SMS, Slack
```

**High SMS Cost Alert:**
```yaml
Display Name: "High SMS Usage - Cost Alert"
Condition:
  - Resource: Cloud Function
  - Metric: executions/count for sendOtp
  - Threshold: > 100 calls in 1 hour
Notification: Immediate email
```

**Rate Limit Exceeded Alert:**
```yaml
Display Name: "Rate Limit Abuse Detected"
Condition:
  - Log-based metric for "Rate limit exceeded"
  - Threshold: > 50 occurrences in 10 minutes
Notification: Urgent - SMS + Email
```

## 📧 Notification Channels

### Email Notifications:
```bash
# Set up email notification channel
gcloud alpha monitoring channels create \
  --display-name="Admin Email" \
  --type=email \
  --channel-labels=email_address=<EMAIL>
```

### SMS Notifications:
```bash
# Set up SMS notification channel
gcloud alpha monitoring channels create \
  --display-name="Admin SMS" \
  --type=sms \
  --channel-labels=number=+1234567890
```

### Slack Integration:
1. Create Slack webhook URL
2. Add webhook as notification channel in Google Cloud Console

## 📈 Custom Dashboards

### Create Monitoring Dashboard:

```javascript
// Add to your functions for custom metrics
const { Monitoring } = require('@google-cloud/monitoring');
const monitoring = new Monitoring.MetricServiceClient();

// Custom metric for OTP requests
async function recordOTPMetric(phoneNumber: string, success: boolean) {
  const projectId = 'towasl';
  const metricType = 'custom.googleapis.com/otp_requests';
  
  const dataPoint = {
    interval: {
      endTime: {
        seconds: Date.now() / 1000,
      },
    },
    value: {
      int64Value: 1,
    },
  };

  const timeSeriesData = {
    metric: {
      type: metricType,
      labels: {
        success: success.toString(),
        country: phoneNumber.substring(0, 4), // Country code
      },
    },
    resource: {
      type: 'global',
      labels: {
        project_id: projectId,
      },
    },
    points: [dataPoint],
  };

  await monitoring.createTimeSeries({
    name: monitoring.projectPath(projectId),
    timeSeries: [timeSeriesData],
  });
}
```

## 🔍 Log-Based Monitoring

### Important Log Patterns to Monitor:

```bash
# Monitor these patterns in Firebase Functions logs:

# 1. Rate limit abuse
"Rate limit exceeded"

# 2. Invalid API keys
"Invalid API key"

# 3. SMS failures
"Failed to send SMS"

# 4. High OTP request volume
"OTP sent successfully"

# 5. Suspicious activity
"Invalid phone number format"
```

### Create Log-Based Metrics:

1. **Go to Google Cloud Console → Logging**
2. **Create log-based metrics**
3. **Set up alerts based on these metrics**

Example log-based metric:
```
Name: rate_limit_exceeded_count
Filter: resource.type="cloud_function" AND textPayload:"Rate limit exceeded"
Metric Type: Counter
```

## 📱 Real-Time Monitoring Setup

### 1. **Sentry Integration** (Recommended)

```bash
# Install Sentry
npm install @sentry/node @sentry/integrations
```

```typescript
// Add to functions/src/index.ts
import * as Sentry from '@sentry/node';

// Initialize Sentry
Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
  environment: 'production',
});

// Add to your functions
export const sendOtp = functions.https.onRequest(async (request, response) => {
  return corsHandler(request, response, async () => {
    try {
      // ... your function code
    } catch (error) {
      Sentry.captureException(error);
      functions.logger.error("Error in sendOtp:", error);
      response.status(500).json({ error: "Internal server error" });
    }
  });
});
```

### 2. **Custom Health Check Function**

```typescript
// Add health check endpoint
export const healthCheck = functions.https.onRequest(async (request, response) => {
  try {
    // Check database connectivity
    await db.collection('health').doc('check').get();
    
    // Check SMS service configuration
    if (!MSEGAT_CONFIG.username || !MSEGAT_CONFIG.apiKey) {
      throw new Error('SMS configuration missing');
    }
    
    response.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'ok',
        sms: 'ok'
      }
    });
  } catch (error) {
    response.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});
```

## 📊 Key Metrics to Track

### 1. **Business Metrics**
- OTP requests per day/hour
- Successful verifications rate
- User signup/login rate
- SMS delivery success rate

### 2. **Technical Metrics**
- Function execution time
- Error rates by function
- Memory and CPU usage
- API response times

### 3. **Security Metrics**
- Rate limit violations
- Invalid API key attempts
- Suspicious IP activity
- Failed OTP attempts

### 4. **Cost Metrics**
- SMS costs per day
- Function execution costs
- Database read/write costs
- Total Firebase bill

## 🚨 Alert Thresholds (Recommended)

```yaml
Critical Alerts (Immediate):
  - Error rate > 5% for 5 minutes
  - Function down for > 2 minutes
  - SMS cost > $100/day
  - Rate limit abuse > 100/hour

Warning Alerts (15 min delay):
  - Error rate > 2% for 10 minutes
  - Response time > 5 seconds
  - SMS cost > $50/day
  - High memory usage > 80%

Info Alerts (1 hour delay):
  - Daily usage reports
  - Weekly cost summaries
  - Performance trends
```

## 📧 Sample Alert Email Template

```
Subject: 🚨 URGENT: High Error Rate Detected - Towasl API

Alert: High Error Rate
Service: Towasl Backend Functions
Time: 2024-01-15 14:30:00 UTC
Severity: CRITICAL

Details:
- Error rate: 8.5% (threshold: 5%)
- Affected function: sendOtp
- Duration: 7 minutes
- Total errors: 23 in last 5 minutes

Possible causes:
- SMS service outage
- Database connectivity issues
- High traffic volume

Action required:
1. Check Firebase Functions logs
2. Verify SMS service status
3. Monitor traffic patterns

Dashboard: https://console.firebase.google.com/project/towasl/functions
```

## 🔧 Monitoring Checklist

- [ ] Firebase Console monitoring enabled
- [ ] Budget alerts configured
- [ ] Error rate alerts set up
- [ ] SMS cost monitoring active
- [ ] Log-based metrics created
- [ ] Notification channels configured
- [ ] Health check endpoint deployed
- [ ] Custom dashboards created
- [ ] Team notification preferences set
- [ ] Emergency response plan documented
