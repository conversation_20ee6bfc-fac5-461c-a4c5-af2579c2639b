# Towasl Backend - Firebase Functions Setup

This project contains Firebase Functions for SMS OTP authentication and user management using Msegat SMS service.

## Project Structure

```
towasl-backend/
├── functions/
│   ├── src/
│   │   └── index.ts          # Main functions file
│   ├── package.json          # Node.js dependencies
│   ├── tsconfig.json         # TypeScript configuration
│   └── .eslintrc.js          # ESLint configuration
├── firebase.json             # Firebase project configuration
├── firestore.rules           # Firestore security rules
├── firestore.indexes.json    # Firestore indexes
└── .firebaserc              # Firebase project settings
```

## Functions

### 1. sendOtp
- **Endpoint**: `POST /sendOtp`
- **Purpose**: Generate and send 4-digit OTP via SMS
- **Request Body**: `{ countryCode: string, mobile: string }`
- **Response**: `{ success: boolean, message: string, expiryTime: number }`

### 2. verifyOtpAndSignupLogin
- **Endpoint**: `POST /verifyOtpAndSignupLogin`
- **Purpose**: Verify OTP and handle user authentication
- **Request Body**: `{ countryCode: string, mobile: string, otp: string, userData?: object }`
- **Response**: `{ success: boolean, message: string, customToken: string, user: object }`
- **Note**: userData is optional and typically contains device/app information

## Setup Instructions

### 1. Install Dependencies
```bash
cd functions
npm install
```

### 2. Configure Environment Variables
Set up Msegat credentials and API security using Firebase Functions config:

⚠️ **IMPORTANT**: Replace the placeholder values below with your actual Msegat credentials.

```bash
# Msegat SMS Service Configuration
firebase functions:config:set msegat.username="YOUR_MSEGAT_USERNAME"
firebase functions:config:set msegat.api_key="YOUR_MSEGAT_API_KEY"
firebase functions:config:set msegat.sender_id="YOUR_SENDER_ID"
firebase functions:config:set msegat.message_template="Pin Code is: xxxx"

# API Security Configuration (CRITICAL for production)
API_KEY=$(openssl rand -hex 16)
echo "🔑 Generated API Key: $API_KEY"
echo "📝 IMPORTANT: Save this key - you'll need it for your Flutter app!"
firebase functions:config:set api.key="$API_KEY"
firebase functions:config:set api.require_key="true"

# Verify all configuration
firebase functions:config:get
```

**Security Notes**:
- Never commit actual credentials to version control
- **Save the generated API key securely** - you'll need it for your Flutter app
- The functions will validate that these environment variables are set before processing requests

### 3. Deploy Functions
```bash
# Build the functions
npm run build

# Deploy to Firebase
firebase deploy --only functions
```

### 4. Deploy Firestore Rules
```bash
firebase deploy --only firestore:rules
```

## Development

### Local Development
```bash
# Start the Firebase emulator
npm run serve

# Watch for changes
npm run build:watch
```

### Testing
The functions will be available at:
- `http://localhost:5001/towasl/us-central1/sendOtp`
- `http://localhost:5001/towasl/us-central1/verifyOtpAndSignupLogin`

## Security Features

1. **CORS Protection**: Configured to handle cross-origin requests
2. **Input Validation**: Phone number format and OTP validation
3. **OTP Expiry**: 5-minute expiration for OTPs
4. **Firestore Security**: Client-side access denied to OTP collection
5. **User Data Protection**: Users can only access their own data

## Firestore Collections

### otp_requests
- **Document ID**: Phone number
- **Fields**: `otp`, `createdAt`, `expiryTime`, `verified`
- **Access**: Server-side only

### users
- **Document ID**: Firebase Auth UID
- **Fields**: `phoneNumber`, `countryCode`, `mobile`, `duid`, `createdAt`, `lastLoginAt`, custom user data
- **Access**: Authenticated users can read/write their own document

## Error Handling

The functions include comprehensive error handling for:
- Invalid phone numbers
- Expired or invalid OTPs
- SMS sending failures
- Database errors
- Authentication errors

## Logging

All functions include detailed logging for debugging and monitoring purposes.
