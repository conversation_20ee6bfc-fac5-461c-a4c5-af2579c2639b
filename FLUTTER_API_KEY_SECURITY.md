# Flutter API Key Security Guide

## 🚨 Critical Security Warning

**NEVER hardcode API keys directly in Flutter code!** API keys in mobile apps can be extracted by anyone who downloads your app.

## ✅ Secure Methods for API Keys in Flutter

### Method 1: Environment Variables (Recommended)

#### Step 1: Create dart-define variables

```bash
# Build with environment variables
flutter build apk --dart-define=API_KEY=your-api-key-here
flutter build ios --dart-define=API_KEY=your-api-key-here

# For development
flutter run --dart-define=API_KEY=your-dev-api-key-here
```

#### Step 2: Access in Flutter code

```dart
// lib/config/app_config.dart
class AppConfig {
  static const String apiKey = String.fromEnvironment(
    'API_KEY',
    defaultValue: '', // Empty default for security
  );
  
  static bool get isApiKeyConfigured => apiKey.isNotEmpty;
}

// lib/services/auth_service.dart
import '../config/app_config.dart';

class AuthService {
  static const String baseUrl = 'https://us-central1-towasl.cloudfunctions.net';
  
  Future<bool> sendOtp({
    required String countryCode,
    required String mobile,
  }) async {
    // Validate API key is configured
    if (!AppConfig.isApiKeyConfigured) {
      throw Exception('API key not configured');
    }
    
    final response = await http.post(
      Uri.parse('$baseUrl/sendOtp'),
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': AppConfig.apiKey, // Secure access
      },
      body: jsonEncode({
        'countryCode': countryCode,
        'mobile': mobile,
      }),
    );
    
    return response.statusCode == 200;
  }
}
```

#### Step 3: Create development and build scripts

**Create these scripts in your Flutter project root:**

```bash
# scripts/run_dev.sh
#!/bin/bash
flutter run --dart-define=API_KEY=your_dev_key_here --dart-define=ENV=development

# scripts/run_prod.sh
#!/bin/bash
flutter run --dart-define=API_KEY=your_prod_key_here --dart-define=ENV=production

# scripts/build_dev_android.sh
#!/bin/bash
flutter build apk --dart-define=API_KEY=your_dev_key_here --dart-define=ENV=development

# scripts/build_prod_android.sh
#!/bin/bash
flutter build apk --dart-define=API_KEY=your_prod_key_here --dart-define=ENV=production

# scripts/build_dev_ios.sh
#!/bin/bash
flutter build ios --dart-define=API_KEY=your_dev_key_here --dart-define=ENV=development

# scripts/build_prod_ios.sh
#!/bin/bash
flutter build ios --dart-define=API_KEY=your_prod_key_here --dart-define=ENV=production
```

**Make scripts executable:**
```bash
chmod +x scripts/*.sh
```

**Usage:**
```bash
# Development
./scripts/run_dev.sh

# Production testing
./scripts/run_prod.sh

# Build for Android
./scripts/build_prod_android.sh

# Build for iOS
./scripts/build_prod_ios.sh
```

#### Step 4: VS Code Launch Configuration (IDE Integration)

**Create `.vscode/launch.json` in your Flutter project:**

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Flutter Development",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--dart-define=API_KEY=your_dev_key_here",
                "--dart-define=ENV=development"
            ]
        },
        {
            "name": "Flutter Production",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--dart-define=API_KEY=your_prod_key_here",
                "--dart-define=ENV=production"
            ]
        }
    ]
}
```

**Usage in VS Code:**
1. Press `F5` or go to Run → Start Debugging
2. Select "Flutter Development" or "Flutter Production"
3. App runs with the correct API key automatically

#### Step 5: Android Studio Run Configuration

**In Android Studio:**
1. Go to Run → Edit Configurations
2. Select your Flutter configuration
3. In "Additional run args" add:
   ```
   --dart-define=API_KEY=your_key_here --dart-define=ENV=development
   ```
4. Create separate configurations for dev/prod

### Method 2: Flutter Dotenv (Alternative)

#### Step 1: Add dependency

```yaml
# pubspec.yaml
dependencies:
  flutter_dotenv: ^5.1.0
```

#### Step 2: Create environment files

```bash
# .env.development (add to .gitignore)
API_KEY=dev_****************************************
BASE_URL=http://localhost:5001/towasl/us-central1

# .env.production (add to .gitignore)
API_KEY=prod_****************************************
BASE_URL=https://us-central1-towasl.cloudfunctions.net
```

#### Step 3: Load environment

```dart
// main.dart
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment file
  await dotenv.load(fileName: ".env.${const String.fromEnvironment('ENV', defaultValue: 'development')}");
  
  runApp(MyApp());
}

// lib/config/app_config.dart
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConfig {
  static String get apiKey => dotenv.env['API_KEY'] ?? '';
  static String get baseUrl => dotenv.env['BASE_URL'] ?? '';
  
  static bool get isConfigured => apiKey.isNotEmpty && baseUrl.isNotEmpty;
}
```

### Method 3: Firebase Remote Config (Most Secure)

#### Step 1: Setup Firebase Remote Config

```yaml
# pubspec.yaml
dependencies:
  firebase_remote_config: ^4.3.8
```

#### Step 2: Configure in Firebase Console

1. Go to Firebase Console → Remote Config
2. Add parameter: `api_key_hash` (not the actual key!)
3. Set different values for different app versions

#### Step 3: Implement secure key retrieval

```dart
// lib/services/config_service.dart
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:crypto/crypto.dart';

class ConfigService {
  static FirebaseRemoteConfig? _remoteConfig;
  
  static Future<void> initialize() async {
    _remoteConfig = FirebaseRemoteConfig.instance;
    await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: const Duration(hours: 1),
    ));
    
    // Set defaults
    await _remoteConfig!.setDefaults({
      'api_key_hash': '',
      'api_endpoint': 'https://us-central1-towasl.cloudfunctions.net',
    });
    
    await _remoteConfig!.fetchAndActivate();
  }
  
  // Don't store actual API key - use a hash or encrypted version
  static String get apiKeyHash => _remoteConfig?.getString('api_key_hash') ?? '';
  static String get apiEndpoint => _remoteConfig?.getString('api_endpoint') ?? '';
  
  // Decrypt or derive actual API key (implement your own logic)
  static String deriveApiKey() {
    // Your custom logic to derive the actual API key
    // This could involve device-specific information, user authentication, etc.
    return 'derived_api_key_based_on_hash_and_device_info';
  }
}
```

## 🔐 Advanced Security Patterns

### Pattern 1: Dynamic API Key Generation

```dart
// Generate API keys dynamically based on user authentication
class SecureAuthService {
  Future<String> getApiKey() async {
    // Get Firebase Auth token
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('User not authenticated');
    
    final idToken = await user.getIdToken();
    
    // Call a secure function to get API key
    final response = await http.post(
      Uri.parse('$baseUrl/getApiKey'),
      headers: {
        'Authorization': 'Bearer $idToken',
        'Content-Type': 'application/json',
      },
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['apiKey'];
    }
    
    throw Exception('Failed to get API key');
  }
}
```

### Pattern 2: Time-based API Keys

```dart
// API keys that expire and refresh automatically
class TimedApiKeyService {
  String? _cachedApiKey;
  DateTime? _keyExpiry;
  
  Future<String> getValidApiKey() async {
    if (_cachedApiKey == null || 
        _keyExpiry == null || 
        DateTime.now().isAfter(_keyExpiry!)) {
      await _refreshApiKey();
    }
    
    return _cachedApiKey!;
  }
  
  Future<void> _refreshApiKey() async {
    // Call your backend to get a fresh API key
    final response = await http.post(
      Uri.parse('$baseUrl/refreshApiKey'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'deviceId': await _getDeviceId(),
        'appVersion': await _getAppVersion(),
      }),
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      _cachedApiKey = data['apiKey'];
      _keyExpiry = DateTime.now().add(Duration(hours: data['expiresInHours']));
    }
  }
}
```

## 🛡️ Additional Security Measures

### 1. Certificate Pinning

```dart
// Pin your API certificates
class SecureHttpClient {
  static final HttpClient _httpClient = HttpClient()
    ..badCertificateCallback = (cert, host, port) {
      // Implement certificate pinning logic
      return _isValidCertificate(cert, host);
    };
    
  static bool _isValidCertificate(X509Certificate cert, String host) {
    // Your certificate validation logic
    return true; // Implement proper validation
  }
}
```

### 2. Request Signing

```dart
// Sign requests with device-specific information
class SignedRequestService {
  Future<Map<String, String>> getSignedHeaders() async {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final deviceId = await _getDeviceId();
    final signature = _generateSignature(timestamp, deviceId);
    
    return {
      'Content-Type': 'application/json',
      'X-API-Key': await _getApiKey(),
      'X-Timestamp': timestamp,
      'X-Device-ID': deviceId,
      'X-Signature': signature,
    };
  }
  
  String _generateSignature(String timestamp, String deviceId) {
    // Generate HMAC signature
    final key = utf8.encode('your-signing-secret');
    final message = utf8.encode('$timestamp:$deviceId');
    final hmac = Hmac(sha256, key);
    return hmac.convert(message).toString();
  }
}
```

## 🏗️ Production Build Commands

### Android Builds

```bash
# Debug APK (for testing)
flutter build apk --debug --dart-define=API_KEY=your_dev_key

# Release APK (for distribution)
flutter build apk --release --dart-define=API_KEY=your_prod_key

# App Bundle (recommended for Google Play Store)
flutter build appbundle --release --dart-define=API_KEY=your_prod_key
```

### iOS Builds

```bash
# Debug build (for testing on device/simulator)
flutter build ios --debug --dart-define=API_KEY=your_dev_key

# Release build (for App Store or TestFlight)
flutter build ios --release --dart-define=API_KEY=your_prod_key

# Build and archive for App Store (after flutter build ios)
cd ios
xcodebuild -workspace Runner.xcworkspace -scheme Runner -configuration Release archive -archivePath build/Runner.xcarchive

# Or use Xcode GUI after flutter build ios
open ios/Runner.xcworkspace
```

### Cross-Platform Build Script

```bash
# scripts/build_all_prod.sh
#!/bin/bash

API_KEY="your_production_key_here"
ENV="production"

echo "🏗️ Building for all platforms with production config..."

# Android
echo "📱 Building Android APK..."
flutter build apk --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV

echo "📱 Building Android App Bundle..."
flutter build appbundle --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV

# iOS
echo "🍎 Building iOS..."
flutter build ios --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV

echo "✅ All builds completed!"
echo "📁 Android APK: build/app/outputs/flutter-apk/app-release.apk"
echo "📁 Android Bundle: build/app/outputs/bundle/release/app-release.aab"
echo "📁 iOS: build/ios/iphoneos/Runner.app"
```

## 📱 Platform-Specific Security

### Android Security

```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<application
    android:usesCleartextTraffic="false"
    android:networkSecurityConfig="@xml/network_security_config">
    
<!-- android/app/src/main/res/xml/network_security_config.xml -->
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">us-central1-towasl.cloudfunctions.net</domain>
        <pin-set>
            <pin digest="SHA-256">your-certificate-pin-here</pin>
        </pin-set>
    </domain-config>
</network-security-config>
```

### iOS Security

```xml
<!-- ios/Runner/Info.plist -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSExceptionDomains</key>
    <dict>
        <key>us-central1-towasl.cloudfunctions.net</key>
        <dict>
            <key>NSExceptionRequiresForwardSecrecy</key>
            <false/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.2</string>
            <key>NSIncludesSubdomains</key>
            <true/>
        </dict>
    </dict>
</dict>
```

## 🚀 Recommended Implementation for Your App

For your Towasl app, I recommend **Method 1 (Environment Variables)** because:

1. ✅ Simple to implement
2. ✅ Secure (not in source code)
3. ✅ Easy to manage different environments
4. ✅ No additional dependencies
5. ✅ Works well with CI/CD

### Quick Implementation:

```bash
# 1. Build with API key
flutter build apk --dart-define=API_KEY=your-production-key

# 2. Use in code
class AppConfig {
  static const String apiKey = String.fromEnvironment('API_KEY');
}

# 3. Add to headers
'X-API-Key': AppConfig.apiKey
```

## 🔒 Security Checklist

- [ ] API key not hardcoded in source code
- [ ] Environment variables used for configuration
- [ ] Different keys for development/production
- [ ] API keys added to .gitignore
- [ ] Certificate pinning implemented (optional)
- [ ] Request signing implemented (optional)
- [ ] Network security config set up
- [ ] API key rotation plan in place

---

**Remember**: The goal is to make it difficult (not impossible) for attackers to extract your API key. Mobile app security is about layers of protection, not perfect security.
