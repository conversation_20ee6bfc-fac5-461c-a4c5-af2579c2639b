{"name": "towasl-backend-functions", "version": "1.0.0", "description": "Firebase Functions for Towasl SMS OTP and user authentication", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "typescript": "^4.9.0"}, "private": true}