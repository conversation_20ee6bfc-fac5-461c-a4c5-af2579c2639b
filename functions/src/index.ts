import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import cors from "cors";
import axios from "axios";

// Initialize Firebase Admin SDK
admin.initializeApp();

// Initialize CORS with options
const corsHandler = cors({
  origin: functions.config().cors?.allowed_origins?.split(",") || true, // Configurable origins
  methods: ["POST", "OPTIONS"], // Only allow POST and OPTIONS
  allowedHeaders: ["Content-Type", "X-API-Key"],
  credentials: false,
});

// Firestore and Auth references
const db = admin.firestore();
const auth = admin.auth();

// Msegat configuration from environment variables
const MSEGAT_CONFIG = {
  username: functions.config().msegat?.username,
  apiKey: functions.config().msegat?.api_key,
  senderId: functions.config().msegat?.sender_id || "auth-mseg",
  messageTemplate: functions.config().msegat?.message_template || "Pin Code is: xxxx",
  baseUrl: "https://www.msegat.com/gw/sendsms.php",
};

// API Security configuration
const API_CONFIG = {
  apiKey: functions.config().api?.key,
  requireApiKey: functions.config().api?.require_key === "true",
};

// Validate required environment variables
if (!MSEGAT_CONFIG.username || !MSEGAT_CONFIG.apiKey) {
  functions.logger.error("Missing required Msegat configuration. Please set msegat.username and msegat.api_key using Firebase Functions config.");
}

// API Key validation function
function validateApiKey(request: functions.https.Request): boolean {
  // Skip API key validation if not required (for development)
  if (!API_CONFIG.requireApiKey) {
    return true;
  }

  const providedKey = request.headers["x-api-key"] as string;

  if (!API_CONFIG.apiKey) {
    functions.logger.error("API key not configured but required");
    return false;
  }

  if (!providedKey) {
    functions.logger.warn("API key missing from request");
    return false;
  }

  if (providedKey !== API_CONFIG.apiKey) {
    functions.logger.warn("Invalid API key provided");
    return false;
  }

  return true;
}

// Utility function to generate 4-digit OTP
function generateOTP(): string {
  return Math.floor(1000 + Math.random() * 9000).toString();
}

// Utility function to generate 6-character unique displayed user ID (duid)
function generateDUID(): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Utility function to check if DUID already exists
async function isDUIDUnique(duid: string): Promise<boolean> {
  try {
    const querySnapshot = await db.collection("users")
      .where("duid", "==", duid)
      .limit(1)
      .get();
    return querySnapshot.empty;
  } catch (error) {
    functions.logger.error("Error checking DUID uniqueness:", error);
    return false;
  }
}

// Utility function to generate unique DUID
async function generateUniqueDUID(): Promise<string> {
  let duid: string;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  do {
    duid = generateDUID();
    isUnique = await isDUIDUnique(duid);
    attempts++;
  } while (!isUnique && attempts < maxAttempts);

  if (!isUnique) {
    // Fallback: add timestamp to ensure uniqueness
    duid = generateDUID().substring(0, 4) + Date.now().toString().slice(-2);
  }

  return duid;
}

// Utility function to validate phone number format
function isValidPhoneNumber(phone: string): boolean {
  // Basic validation for international phone numbers
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
}

// Rate limiting configuration
const RATE_LIMITS = {
  OTP_PER_PHONE_PER_MINUTE: 1,
  OTP_PER_IP_PER_HOUR: 10,
  VERIFY_ATTEMPTS_PER_OTP: 5,
};

// Rate limiting helper functions
async function checkPhoneRateLimit(phoneNumber: string): Promise<boolean> {
  const now = admin.firestore.Timestamp.now();
  const oneMinuteAgo = admin.firestore.Timestamp.fromMillis(now.toMillis() - 60000);

  const recentRequests = await db.collection("rate_limits")
    .where("phoneNumber", "==", phoneNumber)
    .where("type", "==", "otp_request")
    .where("timestamp", ">", oneMinuteAgo)
    .get();

  return recentRequests.size < RATE_LIMITS.OTP_PER_PHONE_PER_MINUTE;
}

async function checkIPRateLimit(ipAddress: string): Promise<boolean> {
  const now = admin.firestore.Timestamp.now();
  const oneHourAgo = admin.firestore.Timestamp.fromMillis(now.toMillis() - 3600000);

  const recentRequests = await db.collection("rate_limits")
    .where("ipAddress", "==", ipAddress)
    .where("type", "==", "otp_request")
    .where("timestamp", ">", oneHourAgo)
    .get();

  return recentRequests.size < RATE_LIMITS.OTP_PER_IP_PER_HOUR;
}

async function recordRateLimit(phoneNumber: string, ipAddress: string, type: string): Promise<void> {
  await db.collection("rate_limits").add({
    phoneNumber,
    ipAddress,
    type,
    timestamp: admin.firestore.Timestamp.now(),
  });
}

// Function to send SMS via Msegat API
async function sendSMS(phoneNumber: string, message: string): Promise<boolean> {
  try {
    const response = await axios.post(MSEGAT_CONFIG.baseUrl, {
      userName: MSEGAT_CONFIG.username,
      apiKey: MSEGAT_CONFIG.apiKey,
      numbers: phoneNumber,
      userSender: MSEGAT_CONFIG.senderId,
      msg: message,
      msgEncoding: "UTF8",
    }, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    functions.logger.info("Msegat API response:", response.data);

    // Check if the response indicates success
    // Msegat typically returns a success code in the response
    return response.status === 200 && response.data;
  } catch (error) {
    functions.logger.error("Error sending SMS via Msegat:", error);
    return false;
  }
}

/**
 * HTTP Cloud Function to send OTP via SMS
 * Expected request body: { countryCode: string, mobile: string }
 */
export const sendOtp = functions.https.onRequest(async (request, response) => {
  return corsHandler(request, response, async () => {
    try {
      // Validate API key
      if (!validateApiKey(request)) {
        response.status(401).json({ error: "Unauthorized - Invalid or missing API key" });
        return;
      }

      // Check if required configuration is available
      if (!MSEGAT_CONFIG.username || !MSEGAT_CONFIG.apiKey) {
        functions.logger.error("Msegat configuration missing");
        response.status(500).json({ error: "SMS service configuration error" });
        return;
      }

      // Only allow POST requests
      if (request.method !== "POST") {
        response.status(405).json({ error: "Method not allowed" });
        return;
      }

      const { countryCode, mobile } = request.body;

      // Validate required fields
      if (!countryCode || typeof countryCode !== "string") {
        response.status(400).json({ error: "Country code is required" });
        return;
      }

      if (!mobile || typeof mobile !== "string") {
        response.status(400).json({ error: "Mobile number is required" });
        return;
      }

      // Combine country code and mobile number
      const fullPhoneNumber = countryCode + mobile;

      // Validate input
      if (!isValidPhoneNumber(fullPhoneNumber)) {
        response.status(400).json({ error: "Invalid phone number format" });
        return;
      }

      // Get client IP address for rate limiting
      const clientIP = request.headers["x-forwarded-for"] ||
                      request.headers["x-real-ip"] ||
                      request.connection.remoteAddress ||
                      "unknown";

      // Check rate limits
      const phoneRateLimitOk = await checkPhoneRateLimit(fullPhoneNumber);
      const ipRateLimitOk = await checkIPRateLimit(clientIP as string);

      if (!phoneRateLimitOk) {
        functions.logger.warn(`Rate limit exceeded for phone: ${fullPhoneNumber}`);
        response.status(429).json({
          error: "Too many OTP requests for this phone number. Please wait before trying again."
        });
        return;
      }

      if (!ipRateLimitOk) {
        functions.logger.warn(`Rate limit exceeded for IP: ${clientIP}`);
        response.status(429).json({
          error: "Too many OTP requests from this location. Please wait before trying again."
        });
        return;
      }

      // Record rate limit attempt
      await recordRateLimit(fullPhoneNumber, clientIP as string, "otp_request");

      // Generate OTP
      const otp = generateOTP();
      const now = admin.firestore.Timestamp.now();
      const expiryTime = admin.firestore.Timestamp.fromMillis(
        now.toMillis() + (5 * 60 * 1000) // 5 minutes expiry
      );

      // Store OTP in Firestore using full phone number as document ID
      await db.collection("otp_requests").doc(fullPhoneNumber).set({
        otp: otp,
        createdAt: now,
        expiryTime: expiryTime,
        verified: false,
        ipAddress: clientIP, // Track IP for security monitoring
      });

      // Prepare SMS message
      const smsMessage = MSEGAT_CONFIG.messageTemplate.replace("xxxx", otp);

      // Send SMS
      const smsSent = await sendSMS(fullPhoneNumber, smsMessage);

      if (!smsSent) {
        response.status(500).json({ error: "Failed to send SMS" });
        return;
      }

      functions.logger.info(`OTP sent successfully to ${fullPhoneNumber} from IP: ${clientIP}`);

      response.status(200).json({
        success: true,
        message: "OTP sent successfully",
        expiryTime: expiryTime.toMillis(),
      });

    } catch (error) {
      functions.logger.error("Error in sendOtp function:", error);
      response.status(500).json({ error: "Internal server error" });
    }
  });
});

/**
 * HTTP Cloud Function to verify OTP and handle user signup/login
 * Expected request body: { countryCode: string, mobile: string, otp: string, userData?: object }
 */
export const verifyOtpAndSignupLogin = functions.https.onRequest(async (request, response) => {
  return corsHandler(request, response, async () => {
    try {
      // Validate API key
      if (!validateApiKey(request)) {
        response.status(401).json({ error: "Unauthorized - Invalid or missing API key" });
        return;
      }

      // Only allow POST requests
      if (request.method !== "POST") {
        response.status(405).json({ error: "Method not allowed" });
        return;
      }

      const { countryCode, mobile, otp, userData } = request.body;

      // Validate required fields
      if (!countryCode || typeof countryCode !== "string") {
        response.status(400).json({ error: "Country code is required" });
        return;
      }

      if (!mobile || typeof mobile !== "string") {
        response.status(400).json({ error: "Mobile number is required" });
        return;
      }

      // Combine country code and mobile number
      const fullPhoneNumber = countryCode + mobile;

      // Validate input
      if (!otp || typeof otp !== "string") {
        response.status(400).json({ error: "OTP is required" });
        return;
      }

      if (!isValidPhoneNumber(fullPhoneNumber)) {
        response.status(400).json({ error: "Invalid phone number format" });
        return;
      }

      // Retrieve OTP record from Firestore using full phone number
      const otpDoc = await db.collection("otp_requests").doc(fullPhoneNumber).get();

      if (!otpDoc.exists) {
        response.status(400).json({ error: "OTP not found or expired" });
        return;
      }

      const otpData = otpDoc.data();
      if (!otpData) {
        response.status(400).json({ error: "Invalid OTP data" });
        return;
      }

      // Check if OTP has expired
      const now = admin.firestore.Timestamp.now();
      if (now.toMillis() > otpData.expiryTime.toMillis()) {
        // Clean up expired OTP
        await db.collection("otp_requests").doc(fullPhoneNumber).delete();
        response.status(400).json({ error: "OTP has expired" });
        return;
      }

      // Verify OTP
      if (otpData.otp !== otp) {
        response.status(400).json({ error: "Invalid OTP" });
        return;
      }

      // Check if OTP has already been verified
      if (otpData.verified) {
        response.status(400).json({ error: "OTP has already been used" });
        return;
      }

      // Mark OTP as verified
      await db.collection("otp_requests").doc(fullPhoneNumber).update({
        verified: true,
      });

      // Check if user already exists in Firebase Auth
      let userRecord;
      try {
        // Try to get user by phone number
        const userByPhone = await auth.getUserByPhoneNumber(fullPhoneNumber);
        userRecord = userByPhone;
        functions.logger.info(`Existing user found: ${userRecord.uid}`);
      } catch (error) {
        // User doesn't exist, create new user
        functions.logger.info("Creating new user for phone:", fullPhoneNumber);
        userRecord = await auth.createUser({
          phoneNumber: fullPhoneNumber,
          disabled: false,
        });
        functions.logger.info(`New user created: ${userRecord.uid}`);
      }

      // Create or update user document in Firestore
      const userDocRef = db.collection("users").doc(userRecord.uid);
      const userDoc = await userDocRef.get();

      let userDUID: string;

      if (!userDoc.exists) {
        // Generate unique DUID for new user
        userDUID = await generateUniqueDUID();

        const defaultUserData = {
          phoneNumber: fullPhoneNumber,
          countryCode: countryCode,
          mobile: mobile,
          duid: userDUID,
          createdAt: admin.firestore.Timestamp.now(),
          lastLoginAt: admin.firestore.Timestamp.now(),
        };

        // Create new user document
        await userDocRef.set({
          ...defaultUserData,
          ...userData,
        });
        functions.logger.info(`User document created for UID: ${userRecord.uid} with DUID: ${userDUID}`);
      } else {
        // Get existing DUID or generate new one if missing
        const existingData = userDoc.data();
        userDUID = existingData?.duid;

        if (!userDUID) {
          // Generate DUID for existing user who doesn't have one
          userDUID = await generateUniqueDUID();
          functions.logger.info(`Generated DUID for existing user: ${userRecord.uid} - DUID: ${userDUID}`);
        }

        // Update existing user document
        await userDocRef.update({
          lastLoginAt: admin.firestore.Timestamp.now(),
          countryCode: countryCode,
          mobile: mobile,
          duid: userDUID, // Ensure DUID is always present
          ...(userData && userData),
        });
        functions.logger.info(`User document updated for UID: ${userRecord.uid}`);
      }

      // Create custom token
      const customToken = await auth.createCustomToken(userRecord.uid);

      // Clean up the OTP record after successful verification
      await db.collection("otp_requests").doc(fullPhoneNumber).delete();

      functions.logger.info(`Custom token created for user: ${userRecord.uid}`);

      response.status(200).json({
        success: true,
        message: "OTP verified successfully",
        customToken: customToken,
        user: {
          uid: userRecord.uid,
          phoneNumber: userRecord.phoneNumber,
          countryCode: countryCode,
          mobile: mobile,
          duid: userDUID,
        },
      });

    } catch (error) {
      functions.logger.error("Error in verifyOtpAndSignupLogin function:", error);
      response.status(500).json({ error: "Internal server error" });
    }
  });
});
