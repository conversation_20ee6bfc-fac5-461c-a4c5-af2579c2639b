# Firebase App Check Setup Guide

## 🛡️ What is Firebase App Check?

Firebase App Check protects your backend resources from abuse by verifying that requests come from your authentic apps. It works by:

1. **App Attestation**: Verifies the app is genuine and unmodified
2. **Token Generation**: Creates short-lived tokens for verified apps
3. **Request Validation**: Functions validate tokens before processing
4. **Abuse Prevention**: Blocks requests from unauthorized sources

## 🚀 Step 1: Enable App Check in Firebase Console

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your `towasl` project
3. Navigate to **App Check** in the left sidebar
4. Click **Get Started**

## 📱 Step 2: Configure App Check for Flutter

### Android Setup:

1. **In Firebase Console:**
   - Click on your Android app
   - Select **Play Integrity** as the provider
   - Register your app

2. **In Flutter (android/app/build.gradle):**
   ```gradle
   dependencies {
       implementation 'com.google.firebase:firebase-appcheck-playintegrity:17.1.1'
   }
   ```

3. **In Flutter Code:**
   ```dart
   // main.dart
   import 'package:firebase_app_check/firebase_app_check.dart';

   void main() async {
     WidgetsFlutterBinding.ensureInitialized();
     await Firebase.initializeApp();
     
     // Initialize App Check
     await FirebaseAppCheck.instance.activate(
       androidProvider: AndroidProvider.playIntegrity,
       appleProvider: AppleProvider.appAttest,
     );
     
     runApp(MyApp());
   }
   ```

### iOS Setup:

1. **In Firebase Console:**
   - Click on your iOS app
   - Select **App Attest** as the provider
   - Register your app

2. **In Flutter (ios/Podfile):**
   ```ruby
   pod 'FirebaseAppCheck', '~> 10.0'
   ```

### Flutter Dependencies:

```yaml
# pubspec.yaml
dependencies:
  firebase_app_check: ^0.2.1+8
```

## ⚙️ Step 3: Configure Functions to Use App Check

### Update Firebase Functions:

```bash
# Install App Check for Functions
cd functions
npm install firebase-functions@latest
```

### Update Your Functions Code:

```typescript
// Add to functions/src/index.ts
import { getAppCheck } from 'firebase-admin/app-check';

// App Check validation function
async function validateAppCheck(request: functions.https.Request): Promise<boolean> {
  const appCheckToken = request.headers['x-firebase-appcheck'];
  
  if (!appCheckToken) {
    functions.logger.warn('App Check token missing');
    return false;
  }

  try {
    await getAppCheck().verifyToken(appCheckToken as string);
    return true;
  } catch (error) {
    functions.logger.error('App Check verification failed:', error);
    return false;
  }
}

// Update your functions to include App Check validation
export const sendOtp = functions.https.onRequest(async (request, response) => {
  return corsHandler(request, response, async () => {
    try {
      // Validate App Check token
      const isValidApp = await validateAppCheck(request);
      if (!isValidApp) {
        response.status(401).json({ error: "Unauthorized - Invalid app" });
        return;
      }

      // ... rest of your function code
    } catch (error) {
      // ... error handling
    }
  });
});
```

## 🧪 Step 4: Testing App Check

### Development Testing:

```dart
// For development/testing, you can use debug tokens
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // Enable debug mode for testing
  if (kDebugMode) {
    await FirebaseAppCheck.instance.activate(
      androidProvider: AndroidProvider.debug,
      appleProvider: AppleProvider.debug,
    );
  } else {
    await FirebaseAppCheck.instance.activate(
      androidProvider: AndroidProvider.playIntegrity,
      appleProvider: AppleProvider.appAttest,
    );
  }
  
  runApp(MyApp());
}
```

### Get Debug Token:

```dart
// Add this temporarily to get debug token
FirebaseAppCheck.instance.onTokenChange.listen((token) {
  print('App Check Token: $token');
});
```

### Register Debug Token in Firebase Console:

1. Run your app in debug mode
2. Copy the debug token from console
3. Go to Firebase Console > App Check
4. Add the debug token for testing

## 🔧 Step 5: Production Configuration

### Android Production:
- Uses **Play Integrity API**
- Automatically validates app signature
- No additional setup needed after initial configuration

### iOS Production:
- Uses **App Attest**
- Validates app authenticity
- Works automatically after setup

## 📊 Step 6: Monitoring App Check

### Firebase Console Monitoring:
1. Go to App Check in Firebase Console
2. View metrics for:
   - Valid requests
   - Invalid requests
   - Token generation rates

### Function Logs:
```bash
# Monitor App Check validation
firebase functions:log --filter="App Check"
```

## 🚨 Troubleshooting

### Common Issues:

1. **"App Check token missing"**
   - Ensure App Check is initialized in Flutter
   - Check network connectivity

2. **"App Check verification failed"**
   - Verify app is registered in Firebase Console
   - Check debug tokens are properly configured

3. **"Invalid app signature"**
   - Ensure app is signed with registered certificate
   - Verify bundle ID/package name matches

### Debug Commands:

```bash
# Check App Check status
firebase appcheck:apps:list

# View App Check metrics
firebase appcheck:stats
```

## 🔐 Security Benefits

With App Check enabled:
- ✅ Only your registered apps can call functions
- ✅ Prevents API abuse from bots/scripts
- ✅ Blocks requests from modified/cloned apps
- ✅ Automatic token rotation for security
- ✅ Real-time monitoring and alerts

## 💡 Best Practices

1. **Always enable in production**
2. **Use debug tokens only for development**
3. **Monitor App Check metrics regularly**
4. **Combine with API key authentication**
5. **Test thoroughly before production deployment**

---

**Note**: App Check adds significant security but requires proper setup for each platform. Test thoroughly in development before enabling in production.
