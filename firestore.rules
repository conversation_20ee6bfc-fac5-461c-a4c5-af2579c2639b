rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Deny all access to otp_requests collection from client-side
    match /otp_requests/{document} {
      allow read, write: if false;
    }
    
    // Allow authenticated users to read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Deny all other access by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
