# Flutter Integration Guide

This guide provides detailed instructions for integrating your Flutter app with the Towasl Backend Firebase Functions.

## Overview

The authentication flow works as follows:
1. **Send OTP**: User enters phone number → Backend sends SMS OTP
2. **Verify OTP**: User enters OTP → Backend verifies and returns custom token
3. **Firebase Auth**: Flutter app uses custom token to authenticate with Firebase
4. **Authenticated State**: User can now access Firestore with their UID

## Prerequisites

- Flutter project set up
- Firebase project configured in Flutter
- Towasl Backend deployed and running

## Step 1: Dependencies

Add to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6  # For accessing user data
  http: ^1.1.0
  
dev_dependencies:
  flutter_test:
    sdk: flutter
```

## Step 2: Firebase Configuration

### Android (`android/app/google-services.json`)
### iOS (`ios/Runner/GoogleService-Info.plist`)

Make sure these files are from the same Firebase project (`towasl`) that your backend is deployed to.

## Step 3: Complete Authentication Service

```dart
// services/auth_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AuthService {
  // Replace with your actual function URLs after deployment
  static const String baseUrl = 'https://us-central1-towasl.cloudfunctions.net';
  
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Send OTP to phone number
  Future<AuthResult> sendOtp(String phoneNumber) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/sendOtp'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({'phoneNumber': phoneNumber}),
      );

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return AuthResult.success(
          message: data['message'],
          data: {'expiryTime': data['expiryTime']},
        );
      } else {
        return AuthResult.error(data['error'] ?? 'Failed to send OTP');
      }
    } catch (e) {
      return AuthResult.error('Network error: $e');
    }
  }

  // Verify OTP and login/signup user
  Future<AuthResult> verifyOtpAndLogin({
    required String countryCode,
    required String mobile,
    required String otp,
    Map<String, dynamic>? userData,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/verifyOtpAndSignupLogin'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'countryCode': countryCode,
          'mobile': mobile,
          'otp': otp,
          if (userData != null) 'userData': userData,
        }),
      );

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        // Sign in with custom token
        final customToken = data['customToken'];
        await _auth.signInWithCustomToken(customToken);
        
        return AuthResult.success(
          message: data['message'],
          data: data['user'],
        );
      } else {
        return AuthResult.error(data['error'] ?? 'Failed to verify OTP');
      }
    } catch (e) {
      return AuthResult.error('Authentication error: $e');
    }
  }

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Check if user is logged in
  bool get isLoggedIn => _auth.currentUser != null;

  // Get user data from Firestore
  Future<Map<String, dynamic>?> getUserData() async {
    if (!isLoggedIn) return null;
    
    try {
      final doc = await _firestore
          .collection('users')
          .doc(currentUser!.uid)
          .get();
      
      return doc.data();
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  // Update user data in Firestore
  Future<bool> updateUserData(Map<String, dynamic> data) async {
    if (!isLoggedIn) return false;
    
    try {
      await _firestore
          .collection('users')
          .doc(currentUser!.uid)
          .update(data);
      
      return true;
    } catch (e) {
      print('Error updating user data: $e');
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    await _auth.signOut();
  }

  // Listen to auth state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();
}

// Helper class for handling results
class AuthResult {
  final bool success;
  final String? message;
  final String? error;
  final Map<String, dynamic>? data;

  AuthResult._({
    required this.success,
    this.message,
    this.error,
    this.data,
  });

  factory AuthResult.success({String? message, Map<String, dynamic>? data}) {
    return AuthResult._(
      success: true,
      message: message,
      data: data,
    );
  }

  factory AuthResult.error(String error) {
    return AuthResult._(
      success: false,
      error: error,
    );
  }
}
```

## Step 4: Phone Number Validation

```dart
// utils/phone_validator.dart
class PhoneValidator {
  static bool isValid(String phone) {
    // Remove all non-digit characters except +
    final cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Check if it starts with + and has 10-15 digits
    final regex = RegExp(r'^\+[1-9]\d{1,14}$');
    return regex.hasMatch(cleaned);
  }

  static String format(String phone) {
    // Remove all non-digit characters except +
    return phone.replaceAll(RegExp(r'[^\d+]'), '');
  }

  static String addCountryCode(String phone, String countryCode) {
    if (phone.startsWith('+')) return phone;
    if (phone.startsWith('0')) phone = phone.substring(1);
    return '$countryCode$phone';
  }
}
```

## Step 5: Error Handling

```dart
// utils/error_handler.dart
class ErrorHandler {
  static String getErrorMessage(String error) {
    switch (error.toLowerCase()) {
      case 'phone number is required':
        return 'Please enter your phone number';
      case 'invalid phone number format':
        return 'Please enter a valid phone number';
      case 'otp is required':
        return 'Please enter the OTP';
      case 'invalid otp':
        return 'The OTP you entered is incorrect';
      case 'otp has expired':
        return 'The OTP has expired. Please request a new one';
      case 'otp not found or expired':
        return 'OTP not found. Please request a new one';
      case 'failed to send sms':
        return 'Failed to send SMS. Please try again';
      default:
        return error;
    }
  }
}
```

## Step 6: Testing

### Local Testing (with Firebase Emulator)
```dart
// For testing with local emulator
static const String baseUrl = 'http://localhost:5001/towasl/us-central1';
```

### Production Testing
```dart
// For production
static const String baseUrl = 'https://us-central1-towasl.cloudfunctions.net';
```

## Security Considerations

1. **Phone Number Validation**: Always validate phone numbers on the client side
2. **OTP Expiry**: Handle OTP expiry gracefully (5 minutes)
3. **Rate Limiting**: Implement client-side rate limiting for OTP requests
4. **Error Messages**: Don't expose sensitive information in error messages
5. **HTTPS Only**: Always use HTTPS for API calls

## Firestore Security

With the current Firestore rules, authenticated users can:
- ✅ Read/write their own user document (`/users/{uid}`)
- ❌ Cannot access other users' data
- ❌ Cannot access OTP collection (server-side only)

## Troubleshooting

### Common Issues:

1. **"Network error"**: Check internet connection and function URLs
2. **"Invalid phone number"**: Ensure phone number includes country code
3. **"OTP expired"**: Request a new OTP (5-minute expiry)
4. **"Firebase not initialized"**: Ensure `Firebase.initializeApp()` is called
5. **"Custom token error"**: Check Firebase project configuration

### Debug Tips:

```dart
// Enable Firebase Auth debug logging
FirebaseAuth.instance.setSettings(
  appVerificationDisabledForTesting: true, // Only for testing
);

// Log network requests
print('Sending request to: $baseUrl/sendOtp');
print('Request body: ${jsonEncode(requestBody)}');
```

## Next Steps

1. Implement proper state management (Provider, Riverpod, or Bloc)
2. Add loading states and better UX
3. Implement proper error handling and retry logic
4. Add analytics and crash reporting
5. Test on real devices with actual phone numbers
