# Mobile-Focused API Design

## Philosophy

This API is designed specifically for mobile applications where the primary identifier is the mobile phone number. The design principles are:

1. **Mobile-First**: Phone number is the primary user identifier
2. **Minimal Data**: Only collect essential information
3. **Clean Separation**: Country code and mobile number handled separately
4. **Privacy-Focused**: DUID for public identification instead of phone numbers

## Core User Data Model

### Primary Identifiers
- **Mobile Number**: The core identifier for authentication
- **Country Code**: Separate field for better UX and validation
- **DUID**: Public-facing 6-character unique identifier

### User Document Structure
```json
{
  "phoneNumber": "+************",    // Full phone (for Firebase Auth)
  "countryCode": "+966",             // Country code only
  "mobile": "512345678",             // Mobile number only
  "duid": "A1B2C3",                 // Public identifier
  "createdAt": "2023-12-01T10:00:00Z",
  "lastLoginAt": "2023-12-01T10:00:00Z"
  // No name, email, or other personal data by default
}
```

## API Endpoints

### 1. Send OTP
**Purpose**: Initiate authentication process

```json
POST /sendOtp
{
  "countryCode": "+966",
  "mobile": "512345678"
}
```

**Response**:
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "expiryTime": 1703123456789
}
```

### 2. Verify OTP & Authenticate
**Purpose**: Complete authentication and get user session

**Minimal Request**:
```json
POST /verifyOtpAndSignupLogin
{
  "countryCode": "+966",
  "mobile": "512345678",
  "otp": "1234"
}
```

**With Optional Metadata**:
```json
POST /verifyOtpAndSignupLogin
{
  "countryCode": "+966",
  "mobile": "512345678",
  "otp": "1234",
  "userData": {
    "deviceInfo": "iPhone 15 Pro",
    "appVersion": "1.0.0",
    "platform": "iOS"
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "customToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "uid": "firebase_user_uid",
    "phoneNumber": "+************",
    "countryCode": "+966",
    "mobile": "512345678",
    "duid": "A1B2C3"
  }
}
```

## Why No Name/Email by Default?

### 1. **Mobile-First Approach**
- Mobile number is sufficient for identification
- Users can add profile information later if needed
- Reduces friction during signup

### 2. **Privacy by Design**
- Collect minimal data initially
- Users control what additional information to share
- GDPR and privacy regulation compliance

### 3. **Better UX**
- Faster signup process
- No mandatory form fields beyond phone number
- Users can complete authentication in seconds

### 4. **Flexibility**
- Apps can collect additional data through separate profile setup
- Different apps may need different user information
- Backend remains generic and reusable

## Optional User Data

### When to Use userData Field
The `userData` field should be used for:

1. **Technical Metadata**:
   ```json
   {
     "deviceInfo": "iPhone 15 Pro",
     "appVersion": "1.0.0",
     "platform": "iOS",
     "fcmToken": "device_push_token"
   }
   ```

2. **App-Specific Data**:
   ```json
   {
     "referralCode": "ABC123",
     "marketingSource": "google_ads",
     "language": "ar"
   }
   ```

3. **Preferences**:
   ```json
   {
     "notifications": true,
     "language": "ar",
     "theme": "dark"
   }
   ```

### What NOT to Include
- Personal names (collect separately in profile)
- Email addresses (collect separately if needed)
- Addresses or location data
- Payment information
- Any sensitive personal data

## Profile Management Strategy

### Separate Profile Endpoints
After authentication, apps can implement separate profile management:

```dart
// Separate profile update (not part of auth)
Future<void> updateProfile({
  String? name,
  String? email,
  String? avatar,
}) async {
  // Update user profile separately
  await FirebaseFirestore.instance
    .collection('users')
    .doc(currentUser.uid)
    .update({
      if (name != null) 'name': name,
      if (email != null) 'email': email,
      if (avatar != null) 'avatar': avatar,
    });
}
```

### Progressive Data Collection
1. **Step 1**: Phone verification (this API)
2. **Step 2**: Optional profile setup (separate flow)
3. **Step 3**: App-specific onboarding (as needed)

## Benefits of This Approach

### 1. **Faster Authentication**
- Single-step phone verification
- No mandatory profile forms
- Immediate app access

### 2. **Better Privacy**
- Minimal data collection
- User controls additional data sharing
- Compliance with privacy regulations

### 3. **Cleaner Architecture**
- Authentication separated from profile management
- Reusable across different apps
- Clear separation of concerns

### 4. **Mobile-Optimized**
- Perfect for mobile-first apps
- Reduced form complexity
- Better conversion rates

## Flutter Implementation

### Clean Authentication Flow
```dart
class AuthService {
  // Step 1: Send OTP
  Future<bool> sendOtp(String countryCode, String mobile) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sendOtp'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'countryCode': countryCode,
        'mobile': mobile,
      }),
    );
    
    final data = jsonDecode(response.body);
    return data['success'] == true;
  }

  // Step 2: Verify and authenticate
  Future<User?> verifyOtp({
    required String countryCode,
    required String mobile,
    required String otp,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/verifyOtpAndSignupLogin'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'countryCode': countryCode,
        'mobile': mobile,
        'otp': otp,
        'userData': {
          'deviceInfo': await _getDeviceInfo(),
          'appVersion': await _getAppVersion(),
        },
      }),
    );
    
    final data = jsonDecode(response.body);
    if (data['success'] == true) {
      // Sign in with Firebase
      await FirebaseAuth.instance.signInWithCustomToken(data['customToken']);
      return User.fromJson(data['user']);
    }
    return null;
  }
}
```

### Separate Profile Management
```dart
class ProfileService {
  Future<void> updateProfile({
    String? name,
    String? email,
    String? bio,
  }) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;
    
    await FirebaseFirestore.instance
      .collection('users')
      .doc(user.uid)
      .update({
        if (name != null) 'name': name,
        if (email != null) 'email': email,
        if (bio != null) 'bio': bio,
        'updatedAt': FieldValue.serverTimestamp(),
      });
  }
}
```

## Summary

This mobile-focused API design:
- ✅ **Minimal friction**: Phone number only for auth
- ✅ **Privacy-first**: No unnecessary data collection
- ✅ **Clean separation**: Auth vs profile management
- ✅ **Mobile-optimized**: Perfect for mobile apps
- ✅ **Flexible**: Apps can extend as needed
- ✅ **Secure**: DUID for public identification

The result is a clean, fast, and privacy-respecting authentication system that's perfect for modern mobile applications.
