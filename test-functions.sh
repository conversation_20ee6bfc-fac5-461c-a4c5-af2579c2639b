#!/bin/bash

# Test script for Towasl Backend Functions

echo "🧪 Testing Towasl Backend Functions..."

# Check if curl is available
if ! command -v curl &> /dev/null; then
    echo "❌ curl is not installed. Please install curl to run tests."
    exit 1
fi

# Default base URL (change this to your deployed URL or local emulator)
BASE_URL="http://localhost:5001/towasl/us-central1"

# Check if custom URL is provided
if [ ! -z "$1" ]; then
    BASE_URL="$1"
fi

# Check if API key is provided as second argument
API_KEY=""
if [ ! -z "$2" ]; then
    API_KEY="$2"
fi

echo "🌐 Using base URL: $BASE_URL"

# Test phone number (replace with a real number for actual testing)
TEST_COUNTRY_CODE="+966"
TEST_MOBILE="512345678"

echo ""
echo "🔐 Testing API Key Authentication..."

# Test without API key first
echo "📱 Testing sendOtp without API key (should fail)..."
echo "Request: POST $BASE_URL/sendOtp"

SEND_OTP_NO_KEY=$(curl -s -X POST "$BASE_URL/sendOtp" \
  -H "Content-Type: application/json" \
  -d "{\"countryCode\": \"$TEST_COUNTRY_CODE\", \"mobile\": \"$TEST_MOBILE\"}")

echo "Response: $SEND_OTP_NO_KEY"

# Check if properly rejected without API key
if echo "$SEND_OTP_NO_KEY" | grep -q '"error".*"Unauthorized"'; then
    echo "✅ API key authentication working - properly rejected request without key!"
elif echo "$SEND_OTP_NO_KEY" | grep -q '"success":true'; then
    echo "⚠️  API key authentication disabled - request succeeded without key"
else
    echo "❓ Unexpected response without API key"
fi

echo ""

# Test with API key if provided
if [ ! -z "$API_KEY" ]; then
    echo "📱 Testing sendOtp with API key..."
    echo "Request: POST $BASE_URL/sendOtp (with API key)"

    SEND_OTP_RESPONSE=$(curl -s -X POST "$BASE_URL/sendOtp" \
      -H "Content-Type: application/json" \
      -H "X-API-Key: $API_KEY" \
      -d "{\"countryCode\": \"$TEST_COUNTRY_CODE\", \"mobile\": \"$TEST_MOBILE\"}")

    echo "Response: $SEND_OTP_RESPONSE"

    # Check if the response contains success
    if echo "$SEND_OTP_RESPONSE" | grep -q '"success":true'; then
        echo "✅ sendOtp with API key test passed!"
    else
        echo "❌ sendOtp with API key test failed!"
    fi
else
    echo "⚠️  No API key provided - skipping authenticated test"
    echo "💡 Usage: $0 <base_url> <api_key>"
fi

echo ""
echo "🔐 Testing verifyOtpAndSignupLogin function..."
echo "Note: This will fail without a valid OTP. Use a real OTP for actual testing."

# Test verify function with API key if provided
if [ ! -z "$API_KEY" ]; then
    echo "Request: POST $BASE_URL/verifyOtpAndSignupLogin (with API key)"

    VERIFY_OTP_RESPONSE=$(curl -s -X POST "$BASE_URL/verifyOtpAndSignupLogin" \
      -H "Content-Type: application/json" \
      -H "X-API-Key: $API_KEY" \
      -d "{\"countryCode\": \"$TEST_COUNTRY_CODE\", \"mobile\": \"$TEST_MOBILE\", \"otp\": \"1234\"}")

    echo "Response: $VERIFY_OTP_RESPONSE"

    # This will likely fail with test data, but we can check the response format
    if echo "$VERIFY_OTP_RESPONSE" | grep -q '"error"'; then
        echo "⚠️  verifyOtpAndSignupLogin returned expected error (normal with test data)"
    else
        echo "✅ verifyOtpAndSignupLogin test passed!"
    fi
else
    echo "⚠️  No API key provided - skipping verifyOtpAndSignupLogin test"
fi

echo ""
echo "🏁 Test completed!"
echo ""
echo "💡 Tips:"
echo "- For local testing, start the Firebase emulator first: cd functions && npm run serve"
echo "- For production testing with API key: ./test-functions.sh https://us-central1-towasl.cloudfunctions.net YOUR_API_KEY"
echo "- For testing without API key: ./test-functions.sh https://us-central1-towasl.cloudfunctions.net"
echo "- Replace TEST_COUNTRY_CODE and TEST_MOBILE with real values and use actual OTPs for full testing"
echo "- Get your API key from: firebase functions:config:get"
echo ""
echo "📚 Usage Examples:"
echo "  Local testing:     ./test-functions.sh"
echo "  Production test:   ./test-functions.sh https://us-central1-towasl.cloudfunctions.net your-api-key"
echo "  Security test:     ./test-functions.sh https://us-central1-towasl.cloudfunctions.net"
